# Example configuration for TFRecord → PyTorch data ingestion pipeline
# This file demonstrates all available configuration options

# Required: Directory containing TFRecord files (alternative to explicit file list)
data_ingestion:
  tfrecord_dir: "/path/to/data"   # Directory containing TFRecord files
  file_glob: "*.tfrecord"         # Optional glob pattern (default: "*.tfrecord")

# Alternative: Explicit file list (use either tfrecord_dir or files, not both)
# files:
#   - /path/to/data/train/class_A.tfrecord
#   - /path/to/data/train/class_B.tfrecord

  # Required: Mapping from class labels to integer indices for one-hot encoding
  class_map:
    A: 0
    B: 1
    C: 2

  # TensorFlow data pipeline configuration
  shuffle_buffer: 8192        # Size of shuffle buffer for record-level shuffling
  interleave_cycle: 8         # Number of files to interleave simultaneously
  block_length: 1             # Records per file before switching in interleave cycle
  seed: 123                   # Random seed for reproducible shuffling (null for random)

  # Normalization configuration
  normalization: per_sample   # "per_sample", "per_sample_meanstd", "global", "minmax", "l2", "log1p"
  global_mean: null           # Required if normalization is "global"
  global_std: null            # Required if normalization is "global"
  log1p_center: false         # Whether to center log1p normalization (only for log1p)

  # Optional filtering by plot_id allow-list
  allow_list_path: null       # Path to JSON file with valid plot_ids (null to include all)

  # DataLoader configuration
  batch_size: 64              # Batch size for training
  num_workers: 4              # Number of DataLoader worker processes

  # Metadata configuration
  return_meta: false          # Whether to return metadata dict along with tensors
  drop_last: false            # Whether to drop last incomplete batch
  pin_memory: true            # Whether to pin memory for GPU transfer

# Example usage in Python:
# ```python
# import yaml
# from src.data.tfrecord_dataset import make_train_loader
# 
# # Load configuration
# with open('example_config.yaml', 'r') as f:
#     config = yaml.safe_load(f)
# 
# # Create DataLoader
# dataloader = make_train_loader(config)
# 
# # Use in training loop
# for batch in dataloader:
#     spectra, labels = batch
#     # Process batch...
# ```
